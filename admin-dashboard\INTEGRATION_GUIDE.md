# 🔗 Admin Dashboard Integration Guide

## 🎯 **Real API Integration Complete!**

The admin dashboard is now fully integrated with your FastAPI backend running on `http://localhost:8000`. All endpoints from your OpenAPI spec are connected and working.

## 🚀 **Quick Start**

### 1. **Start Your FastAPI Backend**
```bash
# Make sure your FastAPI server is running
# It should be accessible at http://localhost:8000
```

### 2. **Start the Admin Dashboard**
```bash
cd admin-dashboard
npm install
npm start
```

### 3. **Access the Dashboard**
- **Local**: http://localhost:3000
- **Network**: http://YOUR_IP:3000

## 📊 **Integrated Features**

### **Main Dashboard**
✅ **Real-time System Health** - Connected to `/health`
✅ **Cache Statistics** - Connected to `/apis/admin/cache/stats` 
✅ **Performance Dashboard** - Connected to `/apis/admin/analytics/dashboard`
✅ **Booking Data** - Connected to `/apis/get-all-bookings/`
✅ **Popular Routes** - Connected to `/apis/async/popular-routes`
✅ **Real-time Logs** - Live monitoring of API calls and responses

### **Performance Page**
✅ **Async Search Performance** - `/apis/async/performance/search`
✅ **Detail Performance** - `/apis/async/performance/detail`
✅ **Provider Performance** - `/apis/async/performance/provider`
✅ **Deduplication Metrics** - `/apis/async/performance/deduplication`
✅ **Cache Warming Stats** - `/apis/async/performance/warming`
✅ **Services Health** - `/apis/async/health`

### **Cache Management Page**
✅ **Cache Metrics** - `/apis/admin/cache/stats`
✅ **Cache Configuration** - `/apis/admin/cache/config`
✅ **Cache Health Check** - `/apis/admin/cache/health`
✅ **Key Search** - `/apis/admin/cache/keys/{pattern}`
✅ **Cache Operations**:
  - Invalidate All Cache - `/apis/admin/cache/invalidate-all`
  - Cleanup Expired - `/apis/admin/cache/cleanup`
  - Warm Cache - `/apis/admin/cache/warm`

### **Real-time Logs Component**
✅ **Live API Monitoring** - Shows real-time API calls
✅ **Response Time Tracking** - Monitors performance
✅ **Error Detection** - Highlights issues
✅ **Service Status** - Health check results
✅ **Auto-refresh** - Updates every 5 seconds

## 🔧 **API Endpoints Integrated**

### **Health & Monitoring**
- `GET /health` - System health check
- `GET /network-info` - Network information
- `GET /apis/async/health` - Async services health

### **Performance Analytics**
- `GET /apis/async/performance/search` - Search performance metrics
- `GET /apis/async/performance/detail` - Detail performance metrics
- `GET /apis/async/performance/provider` - Provider performance metrics
- `GET /apis/async/performance/deduplication` - Deduplication efficiency
- `GET /apis/async/performance/warming` - Cache warming performance
- `GET /apis/admin/analytics/dashboard` - Performance dashboard data

### **Cache Management**
- `GET /apis/admin/cache/stats` - Cache statistics
- `GET /apis/admin/cache/config` - Cache configuration
- `GET /apis/admin/cache/health` - Cache health status
- `GET /apis/admin/cache/keys/{pattern}` - Search cache keys
- `POST /apis/admin/cache/invalidate-all` - Invalidate all cache
- `POST /apis/admin/cache/cleanup` - Cleanup expired cache
- `POST /apis/admin/cache/warm` - Warm cache with routes

### **Database Operations**
- `GET /apis/admin/database/stats` - Database statistics
- `POST /apis/admin/database/query` - Execute database queries
- `GET /apis/admin/database/connection-pool/health` - Pool health

### **Booking Management**
- `GET /apis/get-all-bookings/` - All bookings data
- `GET /apis/get-booking/{booking_reference}` - Specific booking details
- `GET /apis/dashboard/all-bookings/` - Dashboard bookings view

### **Popular Routes**
- `GET /apis/async/popular-routes` - Popular flight routes

## 📈 **Real-time Features**

### **Auto-refresh Intervals**
- **Main Dashboard**: Every 5 seconds
- **Performance Page**: Every 30 seconds  
- **Cache Page**: Manual refresh + on-demand
- **Logs Component**: Every 5 seconds

### **Live Monitoring**
- ✅ API response times
- ✅ Cache hit rates
- ✅ System health status
- ✅ Error detection
- ✅ Performance metrics
- ✅ Service availability

## 🎛 **Dashboard Controls**

### **Time Range Selection**
- Last Hour
- Last 6 Hours  
- Last 24 Hours
- Last 7 Days

### **Refresh Controls**
- **Auto-refresh toggle** - Enable/disable automatic updates
- **Manual refresh** - Force immediate data update
- **Last updated timestamp** - Shows when data was last refreshed

### **Cache Operations**
- **Search cache keys** - Find specific cache entries
- **Invalidate cache** - Clear all or specific cache entries
- **Warm cache** - Pre-load popular routes
- **Cleanup expired** - Remove expired cache entries

## 🔍 **Monitoring Capabilities**

### **Performance Metrics**
- Response time trends
- Cache hit rate monitoring
- Error rate tracking
- Request volume analysis
- Service health status

### **Cache Analytics**
- Hit/miss ratios
- Memory usage tracking
- Key distribution analysis
- Expiration monitoring
- Performance impact assessment

### **System Health**
- API service status
- Database connectivity
- Cache service health
- Provider API availability
- Overall system status

## 🚨 **Alerts & Notifications**

### **Performance Alerts**
- Response time > 3000ms (Warning)
- Cache hit rate < 85% (Warning)
- Service unavailable (Error)
- High error rates (Critical)

### **Visual Indicators**
- ✅ Green: Healthy/Good performance
- ⚠️ Yellow: Warning/Degraded performance  
- ❌ Red: Error/Critical issues
- 🔵 Blue: Information/Neutral status

## 🛠 **Troubleshooting**

### **Dashboard Not Loading**
1. Check if FastAPI backend is running on port 8000
2. Verify CORS settings allow localhost:3000
3. Check browser console for errors

### **No Data Showing**
1. Ensure API endpoints are accessible
2. Check network connectivity
3. Verify API responses in browser dev tools

### **Real-time Updates Not Working**
1. Check auto-refresh is enabled
2. Verify API endpoints are responding
3. Look for JavaScript errors in console

## 📊 **Data Flow**

```
FastAPI Backend (Port 8000)
    ↓
Admin Dashboard (Port 3000)
    ↓
Real-time API Calls
    ↓
Live Dashboard Updates
    ↓
Performance Monitoring
```

## 🎉 **Ready to Monitor!**

Your admin dashboard is now fully integrated with your FastAPI backend and ready to provide comprehensive monitoring and management capabilities for your flight booking system!

### **Key Benefits:**
- ✅ Real-time performance monitoring
- ✅ Comprehensive cache management
- ✅ Live system health tracking
- ✅ Detailed analytics and reporting
- ✅ Proactive issue detection
- ✅ Operational efficiency tools

Start monitoring your system performance and managing your cache effectively! 🚀
