import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  CardContent,
  Typography,
  Box,
  Chip,
  List,
  ListItem,
  ListItemText,
  Button,
  FormControl,
  Select,
  MenuItem,
  TextField,
  useTheme,
  alpha,
  CircularProgress,
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Clear as ClearIcon,
  Download as DownloadIcon,
  FilterList as FilterIcon,
} from '@mui/icons-material';
import { useApi } from '../../hooks/useApi';
import { apiService } from '../../services/api';

interface LogEntry {
  timestamp: string;
  level: 'info' | 'warning' | 'error' | 'debug';
  message: string;
  service?: string;
  endpoint?: string;
  response_time?: number;
  status_code?: number;
}

const RealTimeLogs: React.FC = () => {
  const theme = useTheme();
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [filter, setFilter] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Fetch system health and performance data as logs
  const { data: systemHealth, execute: refreshHealth } = useApi(
    () => apiService.getSystemHealth(),
    { immediate: true }
  );

  const { data: cacheStats, execute: refreshCache } = useApi(
    () => apiService.getCacheMetrics(),
    { immediate: true }
  );

  const { data: asyncPerf, execute: refreshAsync } = useApi(
    () => apiService.getAsyncSearchPerformance(),
    { immediate: true }
  );

  const { data: bookings, execute: refreshBookings } = useApi(
    () => apiService.getAllBookings(),
    { immediate: true }
  );

  // Convert API responses to log entries
  useEffect(() => {
    const newLogs: LogEntry[] = [];
    const timestamp = new Date().toISOString();

    if (systemHealth) {
      newLogs.push({
        timestamp,
        level: 'info',
        message: 'System health check completed successfully',
        service: 'health',
        endpoint: '/health',
        status_code: 200,
      });
    }

    if (cacheStats) {
      const hitRate = cacheStats.hit_rate_percentage || 0;
      newLogs.push({
        timestamp,
        level: hitRate >= 85 ? 'info' : 'warning',
        message: `Cache hit rate: ${hitRate.toFixed(1)}% (${cacheStats.hits}/${cacheStats.total_requests})`,
        service: 'cache',
        endpoint: '/apis/admin/cache/stats',
        status_code: 200,
      });
    }

    if (asyncPerf) {
      newLogs.push({
        timestamp,
        level: 'info',
        message: 'Async search performance metrics updated',
        service: 'async-search',
        endpoint: '/apis/async/performance/search',
        status_code: 200,
      });
    }

    if (bookings) {
      newLogs.push({
        timestamp,
        level: 'info',
        message: `Total bookings: ${Array.isArray(bookings) ? bookings.length : 0}`,
        service: 'bookings',
        endpoint: '/apis/get-all-bookings/',
        status_code: 200,
      });
    }

    if (newLogs.length > 0) {
      setLogs(prevLogs => [...newLogs, ...prevLogs].slice(0, 100)); // Keep last 100 logs
    }
  }, [systemHealth, cacheStats, asyncPerf, bookings]);

  // Auto-refresh effect
  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(() => {
        refreshHealth();
        refreshCache();
        refreshAsync();
        refreshBookings();
      }, 5000); // Refresh every 5 seconds

      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshHealth, refreshCache, refreshAsync, refreshBookings]);

  const handleManualRefresh = () => {
    refreshHealth();
    refreshCache();
    refreshAsync();
    refreshBookings();
  };

  const handleClearLogs = () => {
    setLogs([]);
  };

  const getLogColor = (level: string) => {
    switch (level) {
      case 'error':
        return theme.palette.error.main;
      case 'warning':
        return theme.palette.warning.main;
      case 'info':
        return theme.palette.info.main;
      case 'debug':
        return theme.palette.text.secondary;
      default:
        return theme.palette.text.primary;
    }
  };

  const filteredLogs = logs.filter(log => {
    const matchesFilter = filter === 'all' || log.level === filter;
    const matchesSearch = searchTerm === '' || 
      log.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.service?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.endpoint?.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesFilter && matchesSearch;
  });

  return (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            Real-time System Logs
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <Select
                value={filter}
                onChange={(e) => setFilter(e.target.value)}
                startAdornment={<FilterIcon sx={{ mr: 1, fontSize: 16 }} />}
              >
                <MenuItem value="all">All Levels</MenuItem>
                <MenuItem value="info">Info</MenuItem>
                <MenuItem value="warning">Warning</MenuItem>
                <MenuItem value="error">Error</MenuItem>
                <MenuItem value="debug">Debug</MenuItem>
              </Select>
            </FormControl>
            
            <TextField
              size="small"
              placeholder="Search logs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              sx={{ width: 200 }}
            />
            
            <Button
              variant="outlined"
              size="small"
              onClick={() => setAutoRefresh(!autoRefresh)}
              color={autoRefresh ? 'primary' : 'inherit'}
            >
              {autoRefresh ? 'Auto' : 'Manual'}
            </Button>
            
            <Button
              variant="outlined"
              size="small"
              onClick={handleManualRefresh}
              startIcon={<RefreshIcon />}
            >
              Refresh
            </Button>
            
            <Button
              variant="outlined"
              size="small"
              onClick={handleClearLogs}
              startIcon={<ClearIcon />}
            >
              Clear
            </Button>
          </Box>
        </Box>

        <Box
          sx={{
            height: 400,
            overflow: 'auto',
            border: `1px solid ${theme.palette.divider}`,
            borderRadius: 1,
            backgroundColor: alpha(theme.palette.background.paper, 0.5),
          }}
        >
          {filteredLogs.length === 0 ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
              <Typography variant="body2" color="text.secondary">
                No logs available. {autoRefresh ? 'Waiting for updates...' : 'Click refresh to load logs.'}
              </Typography>
            </Box>
          ) : (
            <List dense>
              {filteredLogs.map((log, index) => (
                <ListItem
                  key={index}
                  sx={{
                    borderBottom: `1px solid ${alpha(theme.palette.divider, 0.5)}`,
                    '&:hover': {
                      backgroundColor: alpha(theme.palette.action.hover, 0.05),
                    },
                  }}
                >
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Chip
                          label={log.level.toUpperCase()}
                          size="small"
                          sx={{
                            backgroundColor: alpha(getLogColor(log.level), 0.1),
                            color: getLogColor(log.level),
                            fontWeight: 600,
                            fontSize: '0.75rem',
                          }}
                        />
                        <Typography variant="body2" sx={{ flex: 1 }}>
                          {log.message}
                        </Typography>
                        {log.status_code && (
                          <Chip
                            label={log.status_code}
                            size="small"
                            color={log.status_code === 200 ? 'success' : 'error'}
                          />
                        )}
                      </Box>
                    }
                    secondary={
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 0.5 }}>
                        <Typography variant="caption" color="text.secondary">
                          {new Date(log.timestamp).toLocaleTimeString()}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {log.service && `${log.service} • `}{log.endpoint}
                        </Typography>
                      </Box>
                    }
                  />
                </ListItem>
              ))}
            </List>
          )}
        </Box>

        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 2 }}>
          <Typography variant="caption" color="text.secondary">
            Showing {filteredLogs.length} of {logs.length} logs
          </Typography>
          
          <Typography variant="caption" color="text.secondary">
            {autoRefresh && (
              <>
                <CircularProgress size={12} sx={{ mr: 1 }} />
                Auto-refreshing every 5 seconds
              </>
            )}
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );
};

export default RealTimeLogs;
