{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["C:/Users/<USER>/Documents/PROJECTS/fast_travel_backend/admin-dashboard/src/pages/Performance.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON><PERSON><PERSON>,\n  Box,\n  Card,\n  CardContent,\n  Grid,\n  Button,\n  FormControl,\n  Select,\n  MenuItem,\n  Chip,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  useTheme,\n} from '@mui/material';\nimport {\n  Refresh as RefreshIcon,\n  Speed as PerformanceIcon,\n  Timer as TimerIcon,\n  TrendingUp as TrendingUpIcon,\n} from '@mui/icons-material';\nimport {\n  LineChart,\n  Line,\n  AreaChart,\n  Area,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  Tooltip,\n  ResponsiveContainer,\n  BarChart,\n  Bar,\n} from 'recharts';\nimport { useApi } from '../hooks/useApi';\nimport { apiService } from '../services/api';\nimport MetricsCard from '../components/Dashboard/MetricsCard';\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}