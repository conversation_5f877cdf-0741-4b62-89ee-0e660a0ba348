import React, { useState } from 'react';
import {
  <PERSON>po<PERSON>,
  Box,
  Card,
  CardContent,
  Grid,
  Button,
  TextField,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  useTheme,
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Delete as DeleteIcon,
  Clear as ClearIcon,
  Whatshot as WarmIcon,
  Storage as StorageIcon,
  Speed as SpeedIcon,
  Memory as MemoryIcon,
} from '@mui/icons-material';
import { useApi } from '../hooks/useApi';
import { apiService } from '../services/api';
import MetricsCard from '../components/Dashboard/MetricsCard';
import toast from 'react-hot-toast';

const CachePage: React.FC = () => {
  const theme = useTheme();
  const [searchPattern, setSearchPattern] = useState('*');
  const [warmupRoutes, setWarmupRoutes] = useState('');
  const [confirmDialog, setConfirmDialog] = useState<{ open: boolean; action: string; title: string }>({
    open: false,
    action: '',
    title: '',
  });

  // API hooks
  const { data: cacheStats, loading: statsLoading, execute: refreshStats } = useApi(
    () => apiService.getCacheMetrics(),
    { immediate: true }
  );

  const { data: cacheConfig, loading: configLoading, execute: refreshConfig } = useApi(
    () => apiService.getCacheConfiguration(),
    { immediate: true }
  );

  const { data: cacheHealth, loading: healthLoading, execute: refreshHealth } = useApi(
    () => apiService.getCacheHealthCheck(),
    { immediate: true }
  );

  const { data: cacheKeys, loading: keysLoading, execute: searchKeys } = useApi(
    () => apiService.getCacheKeysByPattern(searchPattern),
    { immediate: false }
  );

  const handleSearchKeys = () => {
    if (searchPattern.trim()) {
      searchKeys();
    }
  };

  const handleInvalidateAll = async () => {
    try {
      const response = await apiService.invalidateAllCache();
      if (response.status === 'success') {
        toast.success('All cache invalidated successfully');
        refreshStats();
      } else {
        toast.error('Failed to invalidate cache');
      }
    } catch (error) {
      toast.error('Error invalidating cache');
    }
    setConfirmDialog({ open: false, action: '', title: '' });
  };

  const handleCleanupExpired = async () => {
    try {
      const response = await apiService.cleanupExpiredCache();
      if (response.status === 'success') {
        toast.success('Expired cache cleaned up successfully');
        refreshStats();
      } else {
        toast.error('Failed to cleanup expired cache');
      }
    } catch (error) {
      toast.error('Error cleaning up cache');
    }
    setConfirmDialog({ open: false, action: '', title: '' });
  };

  const handleWarmCache = async () => {
    try {
      const routes = warmupRoutes.split('\n').filter(route => route.trim());
      const response = await apiService.warmCache({ routes });
      if (response.status === 'success') {
        toast.success('Cache warming initiated successfully');
        setWarmupRoutes('');
      } else {
        toast.error('Failed to warm cache');
      }
    } catch (error) {
      toast.error('Error warming cache');
    }
  };

  const handleRefreshAll = () => {
    refreshStats();
    refreshConfig();
    refreshHealth();
    if (searchPattern.trim()) {
      searchKeys();
    }
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box>
          <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
            Cache Management
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Monitor and manage cache performance and operations
          </Typography>
        </Box>

        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={handleRefreshAll}
        >
          Refresh All
        </Button>
      </Box>

      {/* Cache Metrics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <MetricsCard
            title="Hit Rate"
            value={cacheStats?.hit_rate_percentage || 0}
            unit="%"
            subtitle={`${cacheStats?.hits || 0} hits / ${cacheStats?.total_requests || 0} requests`}
            status={cacheStats?.hit_rate_percentage >= 85 ? 'success' : 'warning'}
            progress={{
              value: cacheStats?.hit_rate_percentage || 0,
              max: 100,
              label: 'Hit Rate'
            }}
            icon={<SpeedIcon />}
            loading={statsLoading}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <MetricsCard
            title="Total Keys"
            value={cacheStats?.total_keys || 0}
            subtitle="Cached entries"
            status="info"
            icon={<StorageIcon />}
            loading={statsLoading}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <MetricsCard
            title="Memory Usage"
            value={cacheStats?.memory_usage_mb || 0}
            unit="MB"
            subtitle="Cache memory consumption"
            status={cacheStats?.memory_usage_mb <= 1000 ? 'success' : 'warning'}
            icon={<MemoryIcon />}
            loading={statsLoading}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <MetricsCard
            title="Cache Health"
            value={cacheHealth ? "Healthy" : "Unknown"}
            subtitle="Redis connection status"
            status={cacheHealth ? 'success' : 'error'}
            icon={<StorageIcon />}
            loading={healthLoading}
          />
        </Grid>
      </Grid>
    </Box>
  );
};

export default CachePage;
