# auth_service/app/config.py
from app.config import env
from databases import Database
from sqlalchemy import create_engine, MetaData

ACCESS_TOKEN_EXPIRE_MINUTES = int(env.get("ACCESS_TOKEN_EXPIRE_MINUTES", 6000))
SECRET_KEY="your_jwt_secret_key"

DATABASE_URL = env.get("AUTH_SERVICE_DATABASE_URL")
database = Database(DATABASE_URL)
metadata = MetaData()
engine = create_engine(DATABASE_URL, echo=True)