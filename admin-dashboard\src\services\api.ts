import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { ApiResponse, PerformanceMetrics, SystemHealth, CacheMetrics } from '../types';

class ApiService {
  private api: AxiosInstance;
  private baseURL: string;

  constructor() {
    this.baseURL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000';

    this.api = axios.create({
      baseURL: this.baseURL,
      timeout: parseInt(process.env.REACT_APP_API_TIMEOUT || '30000'),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // Request interceptor
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        // Add request ID for tracking
        config.headers['X-Request-ID'] = this.generateRequestId();

        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.api.interceptors.response.use(
      (response: AxiosResponse) => {
        return response;
      },
      (error) => {
        if (error.response?.status === 401) {
          // Handle unauthorized access
          localStorage.removeItem('auth_token');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Generic API methods
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await this.api.get(url, config);
      return {
        data: response.data,
        status: 'success',
        timestamp: new Date().toISOString(),
      };
    } catch (error: any) {
      return {
        status: 'error',
        message: error.response?.data?.message || error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await this.api.post(url, data, config);
      return {
        data: response.data,
        status: 'success',
        timestamp: new Date().toISOString(),
      };
    } catch (error: any) {
      return {
        status: 'error',
        message: error.response?.data?.message || error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await this.api.put(url, data, config);
      return {
        data: response.data,
        status: 'success',
        timestamp: new Date().toISOString(),
      };
    } catch (error: any) {
      return {
        status: 'error',
        message: error.response?.data?.message || error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await this.api.delete(url, config);
      return {
        data: response.data,
        status: 'success',
        timestamp: new Date().toISOString(),
      };
    } catch (error: any) {
      return {
        status: 'error',
        message: error.response?.data?.message || error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  // Health and Status endpoints
  async getSystemHealth(): Promise<ApiResponse<SystemHealth>> {
    return this.get<SystemHealth>('/health');
  }

  async getNetworkInfo(): Promise<ApiResponse<any>> {
    return this.get('/network-info');
  }

  // Performance Monitoring endpoints
  async getPerformanceMetrics(timeRange?: string): Promise<ApiResponse<PerformanceMetrics[]>> {
    const params = timeRange ? { time_range: timeRange } : {};
    return this.get<PerformanceMetrics[]>('/apis/async/performance/search', { params });
  }

  async getCacheMetrics(): Promise<ApiResponse<CacheMetrics>> {
    return this.get<CacheMetrics>('/apis/admin/cache/stats');
  }

  async getCacheMonitoringMetrics(): Promise<ApiResponse<any>> {
    return this.get('/apis/admin/cache/monitoring/metrics');
  }

  async getCachePerformanceSummary(hours: number = 1): Promise<ApiResponse<any>> {
    return this.get('/apis/admin/cache/monitoring/summary', { params: { hours } });
  }

  async getCacheAlerts(): Promise<ApiResponse<any>> {
    return this.get('/apis/admin/cache/monitoring/alerts');
  }

  async getSystemStats(): Promise<ApiResponse<any>> {
    return this.get('/apis/admin/performance/system-health');
  }

  // Flight Search Management endpoints
  async getAsyncSearchPerformance(): Promise<ApiResponse<any>> {
    return this.get('/apis/async/performance/search');
  }

  async getAsyncDetailPerformance(): Promise<ApiResponse<any>> {
    return this.get('/apis/async/performance/detail');
  }

  async getAsyncProviderPerformance(): Promise<ApiResponse<any>> {
    return this.get('/apis/async/performance/provider');
  }

  async getDeduplicationPerformance(): Promise<ApiResponse<any>> {
    return this.get('/apis/async/performance/deduplication');
  }

  async getCacheWarmingPerformance(): Promise<ApiResponse<any>> {
    return this.get('/apis/async/performance/warming');
  }

  async getPopularRoutes(limit: number = 20): Promise<ApiResponse<any[]>> {
    return this.get('/apis/async/popular-routes', { params: { limit } });
  }

  async getAsyncServicesHealth(): Promise<ApiResponse<any>> {
    return this.get('/apis/async/health');
  }

  // Cache Management endpoints
  async getCacheKeysByPattern(pattern: string): Promise<ApiResponse<any>> {
    return this.get(`/apis/admin/cache/keys/${encodeURIComponent(pattern)}`);
  }

  async getCacheConfiguration(): Promise<ApiResponse<any>> {
    return this.get('/apis/admin/cache/config');
  }

  async invalidateRouteCache(request: any): Promise<ApiResponse<any>> {
    return this.post('/apis/admin/cache/invalidate', request);
  }

  async invalidateAllCache(): Promise<ApiResponse<any>> {
    return this.post('/apis/admin/cache/invalidate-all');
  }

  async warmCache(request: any): Promise<ApiResponse<any>> {
    return this.post('/apis/admin/cache/warm', request);
  }

  async cleanupExpiredCache(): Promise<ApiResponse<any>> {
    return this.post('/apis/admin/cache/cleanup');
  }

  async getCacheHealthCheck(): Promise<ApiResponse<any>> {
    return this.get('/apis/admin/cache/health');
  }

  // Configuration Management endpoints
  async getConfiguration(): Promise<ApiResponse<any>> {
    return this.get('/apis/admin/config');
  }

  async updateConfiguration(config: any): Promise<ApiResponse<any>> {
    return this.put('/apis/admin/config', config);
  }

  async restartService(service: string): Promise<ApiResponse<any>> {
    return this.post(`/apis/admin/services/${service}/restart`);
  }

  // Database and Analytics endpoints
  async getDatabaseStats(): Promise<ApiResponse<any>> {
    return this.get('/apis/admin/database/stats');
  }

  async executeDatabaseQuery(request: any): Promise<ApiResponse<any>> {
    return this.post('/apis/admin/database/query', request);
  }

  async cleanupDatabaseCache(): Promise<ApiResponse<any>> {
    return this.post('/apis/admin/database/cleanup-cache');
  }

  async checkDatabasePoolHealth(): Promise<ApiResponse<any>> {
    return this.get('/apis/admin/database/connection-pool/health');
  }

  async initializeDatabasePools(): Promise<ApiResponse<any>> {
    return this.post('/apis/admin/database/initialize-pools');
  }

  async closeDatabasePools(): Promise<ApiResponse<any>> {
    return this.post('/apis/admin/database/close-pools');
  }

  // Analytics and Reporting endpoints
  async getPerformanceDashboard(): Promise<ApiResponse<any>> {
    return this.get('/apis/admin/analytics/dashboard');
  }

  async generatePerformanceReport(request: any): Promise<ApiResponse<any>> {
    return this.post('/apis/admin/analytics/report', request);
  }

  async getAnalyticsStats(): Promise<ApiResponse<any>> {
    return this.get('/apis/admin/analytics/stats');
  }

  async recordCustomMetric(metricType: string, value: number, service: string, metadata?: any): Promise<ApiResponse<any>> {
    const params = { metric_type: metricType, value, service };
    return this.post('/apis/admin/analytics/record-metric', metadata, { params });
  }

  // Booking Management endpoints
  async createBookingOptimized(request: any): Promise<ApiResponse<any>> {
    return this.post('/apis/admin/booking/create-optimized', request);
  }

  async getBookingOptimized(request: any): Promise<ApiResponse<any>> {
    return this.post('/apis/admin/booking/get-optimized', request);
  }

  async getUserBookingsOptimized(request: any): Promise<ApiResponse<any>> {
    return this.post('/apis/admin/booking/user-bookings-optimized', request);
  }

  async getBookingServiceStats(): Promise<ApiResponse<any>> {
    return this.get('/apis/admin/booking/service-stats');
  }

  async getAllBookings(): Promise<ApiResponse<any>> {
    return this.get('/apis/get-all-bookings/');
  }

  async getBookingDetails(bookingReference: string): Promise<ApiResponse<any>> {
    return this.get(`/apis/get-booking/${bookingReference}`);
  }

  async getDashboardBookings(): Promise<ApiResponse<any>> {
    return this.get('/apis/dashboard/all-bookings/');
  }

  async getDashboardBookingDetails(bookingReference: string): Promise<ApiResponse<any>> {
    return this.get(`/apis/dashboard/bookings/${bookingReference}`);
  }

  // User Management endpoints (if authentication is enabled)
  async login(credentials: { username: string; password: string }): Promise<ApiResponse<any>> {
    return this.post('/auth/login', credentials);
  }

  async logout(): Promise<ApiResponse<any>> {
    return this.post('/auth/logout');
  }

  async getCurrentUser(): Promise<ApiResponse<any>> {
    return this.get('/auth/me');
  }

  async refreshToken(): Promise<ApiResponse<any>> {
    return this.post('/auth/refresh');
  }
}

export const apiService = new ApiService();
export default apiService;
