import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  FormControl,
  Select,
  MenuItem,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  useTheme,
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Speed as PerformanceIcon,
  Timer as TimerIcon,
  TrendingUp as TrendingUpIcon,
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
} from 'recharts';
import { useApi } from '../hooks/useApi';
import { apiService } from '../services/api';
import MetricsCard from '../components/Dashboard/MetricsCard';

const PerformancePage: React.FC = () => {
  const theme = useTheme();
  const [timeRange, setTimeRange] = useState('1h');
  const [lastRefresh, setLastRefresh] = useState(new Date());

  // API hooks for performance data
  const { data: asyncSearchPerf, loading: searchLoading, execute: refreshSearch } = useApi(
    () => apiService.getAsyncSearchPerformance(),
    { immediate: true }
  );

  const { data: asyncDetailPerf, loading: detailLoading, execute: refreshDetail } = useApi(
    () => apiService.getAsyncDetailPerformance(),
    { immediate: true }
  );

  const { data: providerPerf, loading: providerLoading, execute: refreshProvider } = useApi(
    () => apiService.getAsyncProviderPerformance(),
    { immediate: true }
  );

  const { data: deduplicationPerf, loading: deduplicationLoading, execute: refreshDeduplication } = useApi(
    () => apiService.getDeduplicationPerformance(),
    { immediate: true }
  );

  const { data: warmingPerf, loading: warmingLoading, execute: refreshWarming } = useApi(
    () => apiService.getCacheWarmingPerformance(),
    { immediate: true }
  );

  const { data: servicesHealth, loading: healthLoading, execute: refreshHealth } = useApi(
    () => apiService.getAsyncServicesHealth(),
    { immediate: true }
  );

  const handleRefresh = () => {
    refreshSearch();
    refreshDetail();
    refreshProvider();
    refreshDeduplication();
    refreshWarming();
    refreshHealth();
    setLastRefresh(new Date());
  };

  // Auto-refresh every 30 seconds
  useEffect(() => {
    const interval = setInterval(handleRefresh, 30000);
    return () => clearInterval(interval);
  }, []);

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box>
          <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
            Performance Monitoring
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Real-time performance metrics and analytics
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <Select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
            >
              <MenuItem value="1h">Last Hour</MenuItem>
              <MenuItem value="6h">Last 6 Hours</MenuItem>
              <MenuItem value="24h">Last 24 Hours</MenuItem>
              <MenuItem value="7d">Last 7 Days</MenuItem>
            </Select>
          </FormControl>

          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={handleRefresh}
          >
            Refresh
          </Button>

          <Chip
            label={`Last updated: ${lastRefresh.toLocaleTimeString()}`}
            size="small"
            variant="outlined"
          />
        </Box>
      </Box>

      {/* Performance Metrics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <MetricsCard
            title="Search Performance"
            value={asyncSearchPerf?.avg_response_time || 0}
            unit="ms"
            subtitle="Average response time"
            status={asyncSearchPerf?.avg_response_time <= 3000 ? 'success' : 'warning'}
            icon={<PerformanceIcon />}
            loading={searchLoading}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <MetricsCard
            title="Detail Performance"
            value={asyncDetailPerf?.avg_response_time || 0}
            unit="ms"
            subtitle="Flight detail response"
            status={asyncDetailPerf?.avg_response_time <= 2000 ? 'success' : 'warning'}
            icon={<TimerIcon />}
            loading={detailLoading}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <MetricsCard
            title="Provider Performance"
            value={providerPerf?.avg_response_time || 0}
            unit="ms"
            subtitle="TripJack API response"
            status={providerPerf?.avg_response_time <= 5000 ? 'success' : 'warning'}
            icon={<TrendingUpIcon />}
            loading={providerLoading}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <MetricsCard
            title="Deduplication Rate"
            value={deduplicationPerf?.efficiency_percentage || 0}
            unit="%"
            subtitle="Request deduplication"
            status={deduplicationPerf?.efficiency_percentage >= 70 ? 'success' : 'warning'}
            icon={<PerformanceIcon />}
            loading={deduplicationLoading}
          />
        </Grid>
      </Grid>
    </Box>
  );
};

export default PerformancePage;
