import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>,
  Card,
  CardContent,
  FormControl,
  Select,
  MenuItem,
  Button,
  Chip,
  useTheme,
} from '@mui/material';
import {
  Speed as PerformanceIcon,
  Storage as CacheIcon,
  Search as SearchIcon,
  Error as <PERSON>rrorIcon,
  CloudQueue as TripJackIcon,
  Timer as ResponseTimeIcon,
  TrendingUp as TrendingUpIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
} from 'recharts';
import MetricsCard from '../components/Dashboard/MetricsCard';
import RealTimeLogs from '../components/Dashboard/RealTimeLogs';
import { useAppContext } from '../contexts/AppContext';
import { useApi } from '../hooks/useApi';
import { apiService } from '../services/api';

const Dashboard: React.FC = () => {
  const theme = useTheme();
  const { state } = useAppContext();
  const [timeRange, setTimeRange] = useState('1h');
  const [lastRefresh, setLastRefresh] = useState(new Date());

  // API hooks for real backend data
  const { data: systemHealth, loading: healthLoading, execute: refreshHealth } = useApi(
    () => apiService.getSystemHealth(),
    { immediate: true }
  );

  const { data: cacheStats, loading: cacheLoading, execute: refreshCache } = useApi(
    () => apiService.getCacheMetrics(),
    { immediate: true }
  );

  const { data: performanceDashboard, loading: performanceLoading, execute: refreshPerformance } = useApi(
    () => apiService.getPerformanceDashboard(),
    { immediate: true }
  );

  const { data: asyncSearchPerf, execute: refreshAsyncSearch } = useApi(
    () => apiService.getAsyncSearchPerformance(),
    { immediate: true }
  );

  const { data: popularRoutes, execute: refreshPopularRoutes } = useApi(
    () => apiService.getPopularRoutes(10),
    { immediate: true }
  );

  const { data: allBookings, execute: refreshBookings } = useApi(
    () => apiService.getAllBookings(),
    { immediate: true }
  );

  // Auto-refresh effect
  useEffect(() => {
    if (state.dashboard.autoRefresh) {
      const interval = setInterval(() => {
        refreshHealth();
        refreshCache();
        refreshPerformance();
        refreshAsyncSearch();
        refreshPopularRoutes();
        refreshBookings();
        setLastRefresh(new Date());
      }, state.dashboard.refreshInterval);

      return () => clearInterval(interval);
    }
  }, [state.dashboard.autoRefresh, state.dashboard.refreshInterval, refreshHealth, refreshCache, refreshPerformance, refreshAsyncSearch, refreshPopularRoutes, refreshBookings]);

  const handleManualRefresh = () => {
    refreshHealth();
    refreshCache();
    refreshPerformance();
    refreshAsyncSearch();
    refreshPopularRoutes();
    refreshBookings();
    setLastRefresh(new Date());
  };

  // Mock data for charts (replace with real data)
  const responseTimeData = [
    { time: '00:00', response_time: 1200, target: 3000 },
    { time: '00:05', response_time: 980, target: 3000 },
    { time: '00:10', response_time: 1450, target: 3000 },
    { time: '00:15', response_time: 890, target: 3000 },
    { time: '00:20', response_time: 1100, target: 3000 },
    { time: '00:25', response_time: 750, target: 3000 },
    { time: '00:30', response_time: 1300, target: 3000 },
  ];

  const cacheHitData = [
    { name: 'Cache Hits', value: 85, color: theme.palette.success.main },
    { name: 'Cache Misses', value: 15, color: theme.palette.error.main },
  ];

  const searchStatusData = [
    { name: 'Completed', value: 78, color: theme.palette.success.main },
    { name: 'Pending', value: 12, color: theme.palette.warning.main },
    { name: 'Failed', value: 10, color: theme.palette.error.main },
  ];

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box>
          <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
            System Dashboard
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Real-time monitoring and performance metrics
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <Select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
            >
              <MenuItem value="1h">Last Hour</MenuItem>
              <MenuItem value="6h">Last 6 Hours</MenuItem>
              <MenuItem value="24h">Last 24 Hours</MenuItem>
              <MenuItem value="7d">Last 7 Days</MenuItem>
            </Select>
          </FormControl>

          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={handleManualRefresh}
          >
            Refresh
          </Button>

          <Chip
            label={`Last updated: ${lastRefresh.toLocaleTimeString()}`}
            size="small"
            variant="outlined"
          />
        </Box>
      </Box>

      {/* Key Metrics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <MetricsCard
            title="System Health"
            value={systemHealth ? "Healthy" : "Unknown"}
            subtitle="API Status"
            status={systemHealth ? 'success' : 'warning'}
            trend={{
              direction: 'flat',
              value: 0,
              label: ''
            }}
            icon={<ResponseTimeIcon />}
            loading={healthLoading}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <MetricsCard
            title="Cache Hit Rate"
            value={cacheStats?.hit_rate_percentage || 0}
            unit="%"
            subtitle={`${cacheStats?.hits || 0} hits / ${cacheStats?.total_requests || 0} requests`}
            status={cacheStats?.hit_rate_percentage >= 85 ? 'success' : 'warning'}
            trend={{
              direction: 'up',
              value: 5,
              label: '%'
            }}
            progress={{
              value: cacheStats?.hit_rate_percentage || 0,
              max: 100,
              label: 'Hit Rate'
            }}
            icon={<CacheIcon />}
            loading={cacheLoading}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <MetricsCard
            title="Total Bookings"
            value={Array.isArray(allBookings) ? allBookings.length : 0}
            subtitle="All time bookings"
            status="info"
            trend={{
              direction: 'up',
              value: 12,
              label: '%'
            }}
            icon={<SearchIcon />}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <MetricsCard
            title="Popular Routes"
            value={Array.isArray(popularRoutes) ? popularRoutes.length : 0}
            subtitle="Active routes"
            status="success"
            trend={{
              direction: 'flat',
              value: 0,
              label: ''
            }}
            icon={<TrendingUpIcon />}
            loading={performanceLoading}
          />
        </Grid>
      </Grid>

      {/* Charts Section */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* Response Time Chart */}
        <Grid item xs={12} lg={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                Response Time Trend
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={responseTimeData}>
                  <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />
                  <XAxis
                    dataKey="time"
                    stroke={theme.palette.text.secondary}
                    fontSize={12}
                  />
                  <YAxis
                    stroke={theme.palette.text.secondary}
                    fontSize={12}
                  />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: theme.palette.background.paper,
                      border: `1px solid ${theme.palette.divider}`,
                      borderRadius: 8,
                    }}
                  />
                  <Line
                    type="monotone"
                    dataKey="response_time"
                    stroke={theme.palette.primary.main}
                    strokeWidth={2}
                    dot={{ fill: theme.palette.primary.main, strokeWidth: 2, r: 4 }}
                    activeDot={{ r: 6, stroke: theme.palette.primary.main, strokeWidth: 2 }}
                  />
                  <Line
                    type="monotone"
                    dataKey="target"
                    stroke={theme.palette.error.main}
                    strokeWidth={1}
                    strokeDasharray="5 5"
                    dot={false}
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Cache Performance */}
        <Grid item xs={12} lg={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                Cache Performance
              </Typography>
              <ResponsiveContainer width="100%" height={250}>
                <PieChart>
                  <Pie
                    data={cacheHitData}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={100}
                    paddingAngle={5}
                    dataKey="value"
                  >
                    {cacheHitData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
              <Box sx={{ mt: 2 }}>
                {cacheHitData.map((item, index) => (
                  <Box key={index} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Box
                      sx={{
                        width: 12,
                        height: 12,
                        borderRadius: '50%',
                        backgroundColor: item.color,
                        mr: 1,
                      }}
                    />
                    <Typography variant="body2" sx={{ flex: 1 }}>
                      {item.name}
                    </Typography>
                    <Typography variant="body2" fontWeight={600}>
                      {item.value}%
                    </Typography>
                  </Box>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* System Status Section */}
      <Grid container spacing={3}>
        {/* System Health */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                System Health
              </Typography>
              <Box sx={{ space: 2 }}>
                {[
                  { name: 'API Server', status: state.systemHealth.api, icon: <PerformanceIcon /> },
                  { name: 'Redis Cache', status: state.systemHealth.redis, icon: <CacheIcon /> },
                  { name: 'MySQL Database', status: state.systemHealth.mysql, icon: <CacheIcon /> },
                  { name: 'TripJack API', status: state.systemHealth.tripjack, icon: <TripJackIcon /> },
                ].map((service, index) => (
                  <Box key={index} sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Box sx={{ mr: 2, color: theme.palette.text.secondary }}>
                      {service.icon}
                    </Box>
                    <Typography variant="body1" sx={{ flex: 1 }}>
                      {service.name}
                    </Typography>
                    <Chip
                      label={service.status}
                      size="small"
                      color={
                        service.status === 'healthy' || service.status === 'connected' || service.status === 'available'
                          ? 'success'
                          : service.status === 'degraded' || service.status === 'limited'
                            ? 'warning'
                            : 'error'
                      }
                    />
                  </Box>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Search Status */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                Search Status Distribution
              </Typography>
              <ResponsiveContainer width="100%" height={200}>
                <PieChart>
                  <Pie
                    data={searchStatusData}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    paddingAngle={5}
                    dataKey="value"
                  >
                    {searchStatusData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
              <Box sx={{ mt: 2 }}>
                {searchStatusData.map((item, index) => (
                  <Box key={index} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Box
                      sx={{
                        width: 12,
                        height: 12,
                        borderRadius: '50%',
                        backgroundColor: item.color,
                        mr: 1,
                      }}
                    />
                    <Typography variant="body2" sx={{ flex: 1 }}>
                      {item.name}
                    </Typography>
                    <Typography variant="body2" fontWeight={600}>
                      {item.value}%
                    </Typography>
                  </Box>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Real-time Logs Section */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12}>
          <RealTimeLogs />
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
