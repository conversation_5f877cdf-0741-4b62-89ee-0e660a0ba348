[{"C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\App.tsx": "3", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Cache.tsx": "4", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Dashboard.tsx": "5", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Performance.tsx": "6", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Health.tsx": "7", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Searches.tsx": "8", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Analytics.tsx": "9", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Settings.tsx": "10", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Errors.tsx": "11", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Alerts.tsx": "12", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Export.tsx": "13", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\TripJack.tsx": "14", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Users.tsx": "15", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\NotFound.tsx": "16", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\contexts\\AppContext.tsx": "17", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\components\\Layout\\Layout.tsx": "18", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\hooks\\useWebSocket.ts": "19", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\hooks\\useApi.ts": "20", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\components\\Dashboard\\MetricsCard.tsx": "21", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\services\\websocket.ts": "22", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\components\\Layout\\Sidebar.tsx": "23", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\components\\Layout\\Header.tsx": "24", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\services\\api.ts": "25", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\components\\Dashboard\\RealTimeLogs.tsx": "26"}, {"size": 982, "mtime": 1748262223389, "results": "27", "hashOfConfig": "28"}, {"size": 425, "mtime": 1748262231583, "results": "29", "hashOfConfig": "28"}, {"size": 2261, "mtime": 1748263283719, "results": "30", "hashOfConfig": "28"}, {"size": 5819, "mtime": 1748264424829, "results": "31", "hashOfConfig": "28"}, {"size": 14904, "mtime": 1748264292012, "results": "32", "hashOfConfig": "28"}, {"size": 5588, "mtime": 1748264387675, "results": "33", "hashOfConfig": "28"}, {"size": 488, "mtime": 1748262279502, "results": "34", "hashOfConfig": "28"}, {"size": 496, "mtime": 1748262252696, "results": "35", "hashOfConfig": "28"}, {"size": 498, "mtime": 1748262258888, "results": "36", "hashOfConfig": "28"}, {"size": 492, "mtime": 1748262272658, "results": "37", "hashOfConfig": "28"}, {"size": 482, "mtime": 1748262266073, "results": "38", "hashOfConfig": "28"}, {"size": 488, "mtime": 1748262294411, "results": "39", "hashOfConfig": "28"}, {"size": 484, "mtime": 1748262308425, "results": "40", "hashOfConfig": "28"}, {"size": 506, "mtime": 1748262286713, "results": "41", "hashOfConfig": "28"}, {"size": 490, "mtime": 1748262301190, "results": "42", "hashOfConfig": "28"}, {"size": 934, "mtime": 1748262316748, "results": "43", "hashOfConfig": "28"}, {"size": 9452, "mtime": 1748263396894, "results": "44", "hashOfConfig": "28"}, {"size": 5438, "mtime": 1748263315036, "results": "45", "hashOfConfig": "28"}, {"size": 6491, "mtime": 1748263385943, "results": "46", "hashOfConfig": "28"}, {"size": 5221, "mtime": 1748263374530, "results": "47", "hashOfConfig": "28"}, {"size": 7089, "mtime": 1748263833676, "results": "48", "hashOfConfig": "28"}, {"size": 5900, "mtime": 1748263361985, "results": "49", "hashOfConfig": "28"}, {"size": 10351, "mtime": 1748263906538, "results": "50", "hashOfConfig": "28"}, {"size": 9288, "mtime": 1748263326234, "results": "51", "hashOfConfig": "28"}, {"size": 10253, "mtime": 1748264142585, "results": "52", "hashOfConfig": "28"}, {"size": 9984, "mtime": 1748264265805, "results": "53", "hashOfConfig": "28"}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1kxlmuo", {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 28, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 26, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Cache.tsx", ["132", "133", "134", "135", "136", "137", "138", "139", "140", "141", "142", "143", "144", "145", "146", "147", "148", "149", "150", "151", "152", "153", "154", "155", "156", "157", "158", "159"], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Dashboard.tsx", ["160", "161", "162", "163", "164"], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Performance.tsx", ["165", "166", "167", "168", "169", "170", "171", "172", "173", "174", "175", "176", "177", "178", "179", "180", "181", "182", "183", "184", "185", "186", "187", "188", "189", "190"], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Health.tsx", [], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Searches.tsx", [], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Analytics.tsx", [], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Settings.tsx", [], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Errors.tsx", [], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Alerts.tsx", [], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Export.tsx", [], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\TripJack.tsx", [], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Users.tsx", [], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\NotFound.tsx", [], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\contexts\\AppContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\components\\Layout\\Layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\hooks\\useWebSocket.ts", ["191", "192", "193"], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\hooks\\useApi.ts", ["194"], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\components\\Dashboard\\MetricsCard.tsx", [], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\services\\websocket.ts", ["195"], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\components\\Layout\\Sidebar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\components\\Layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\components\\Dashboard\\RealTimeLogs.tsx", ["196"], [], {"ruleId": "197", "severity": 1, "message": "198", "line": 5, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 5, "endColumn": 7}, {"ruleId": "197", "severity": 1, "message": "201", "line": 6, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 6, "endColumn": 14}, {"ruleId": "197", "severity": 1, "message": "202", "line": 9, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 9, "endColumn": 12}, {"ruleId": "197", "severity": 1, "message": "203", "line": 10, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 10, "endColumn": 7}, {"ruleId": "197", "severity": 1, "message": "204", "line": 11, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 11, "endColumn": 7}, {"ruleId": "197", "severity": 1, "message": "205", "line": 12, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 12, "endColumn": 11}, {"ruleId": "197", "severity": 1, "message": "206", "line": 13, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 13, "endColumn": 15}, {"ruleId": "197", "severity": 1, "message": "207", "line": 14, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 14, "endColumn": 26}, {"ruleId": "197", "severity": 1, "message": "208", "line": 15, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 15, "endColumn": 13}, {"ruleId": "197", "severity": 1, "message": "209", "line": 16, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 16, "endColumn": 9}, {"ruleId": "197", "severity": 1, "message": "210", "line": 17, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 17, "endColumn": 14}, {"ruleId": "197", "severity": 1, "message": "211", "line": 18, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 18, "endColumn": 16}, {"ruleId": "197", "severity": 1, "message": "212", "line": 19, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 19, "endColumn": 16}, {"ruleId": "197", "severity": 1, "message": "213", "line": 20, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 20, "endColumn": 8}, {"ruleId": "197", "severity": 1, "message": "214", "line": 25, "column": 13, "nodeType": "199", "messageId": "200", "endLine": 25, "endColumn": 23}, {"ruleId": "197", "severity": 1, "message": "215", "line": 26, "column": 12, "nodeType": "199", "messageId": "200", "endLine": 26, "endColumn": 21}, {"ruleId": "197", "severity": 1, "message": "216", "line": 27, "column": 15, "nodeType": "199", "messageId": "200", "endLine": 27, "endColumn": 23}, {"ruleId": "197", "severity": 1, "message": "217", "line": 38, "column": 9, "nodeType": "199", "messageId": "200", "endLine": 38, "endColumn": 14}, {"ruleId": "197", "severity": 1, "message": "218", "line": 39, "column": 25, "nodeType": "199", "messageId": "200", "endLine": 39, "endColumn": 41}, {"ruleId": "197", "severity": 1, "message": "219", "line": 41, "column": 10, "nodeType": "199", "messageId": "200", "endLine": 41, "endColumn": 23}, {"ruleId": "197", "severity": 1, "message": "220", "line": 53, "column": 17, "nodeType": "199", "messageId": "200", "endLine": 53, "endColumn": 28}, {"ruleId": "197", "severity": 1, "message": "221", "line": 53, "column": 39, "nodeType": "199", "messageId": "200", "endLine": 53, "endColumn": 52}, {"ruleId": "197", "severity": 1, "message": "222", "line": 63, "column": 17, "nodeType": "199", "messageId": "200", "endLine": 63, "endColumn": 26}, {"ruleId": "197", "severity": 1, "message": "223", "line": 63, "column": 37, "nodeType": "199", "messageId": "200", "endLine": 63, "endColumn": 48}, {"ruleId": "197", "severity": 1, "message": "224", "line": 68, "column": 9, "nodeType": "199", "messageId": "200", "endLine": 68, "endColumn": 25}, {"ruleId": "197", "severity": 1, "message": "225", "line": 74, "column": 9, "nodeType": "199", "messageId": "200", "endLine": 74, "endColumn": 28}, {"ruleId": "197", "severity": 1, "message": "226", "line": 89, "column": 9, "nodeType": "199", "messageId": "200", "endLine": 89, "endColumn": 29}, {"ruleId": "197", "severity": 1, "message": "227", "line": 104, "column": 9, "nodeType": "199", "messageId": "200", "endLine": 104, "endColumn": 24}, {"ruleId": "197", "severity": 1, "message": "228", "line": 19, "column": 12, "nodeType": "199", "messageId": "200", "endLine": 19, "endColumn": 21}, {"ruleId": "197", "severity": 1, "message": "229", "line": 28, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 28, "endColumn": 12}, {"ruleId": "197", "severity": 1, "message": "230", "line": 29, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 29, "endColumn": 7}, {"ruleId": "197", "severity": 1, "message": "231", "line": 62, "column": 17, "nodeType": "199", "messageId": "200", "endLine": 62, "endColumn": 37}, {"ruleId": "197", "severity": 1, "message": "232", "line": 67, "column": 17, "nodeType": "199", "messageId": "200", "endLine": 67, "endColumn": 32}, {"ruleId": "197", "severity": 1, "message": "198", "line": 5, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 5, "endColumn": 7}, {"ruleId": "197", "severity": 1, "message": "201", "line": 6, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 6, "endColumn": 14}, {"ruleId": "197", "severity": 1, "message": "233", "line": 13, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 13, "endColumn": 8}, {"ruleId": "197", "severity": 1, "message": "234", "line": 14, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 14, "endColumn": 12}, {"ruleId": "197", "severity": 1, "message": "235", "line": 15, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 15, "endColumn": 12}, {"ruleId": "197", "severity": 1, "message": "236", "line": 16, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 16, "endColumn": 17}, {"ruleId": "197", "severity": 1, "message": "237", "line": 17, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 17, "endColumn": 12}, {"ruleId": "197", "severity": 1, "message": "238", "line": 18, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 18, "endColumn": 11}, {"ruleId": "197", "severity": 1, "message": "239", "line": 19, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 19, "endColumn": 8}, {"ruleId": "197", "severity": 1, "message": "240", "line": 29, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 29, "endColumn": 12}, {"ruleId": "197", "severity": 1, "message": "241", "line": 30, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 30, "endColumn": 7}, {"ruleId": "197", "severity": 1, "message": "229", "line": 31, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 31, "endColumn": 12}, {"ruleId": "197", "severity": 1, "message": "230", "line": 32, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 32, "endColumn": 7}, {"ruleId": "197", "severity": 1, "message": "242", "line": 33, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 33, "endColumn": 8}, {"ruleId": "197", "severity": 1, "message": "243", "line": 34, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 34, "endColumn": 8}, {"ruleId": "197", "severity": 1, "message": "244", "line": 35, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 35, "endColumn": 16}, {"ruleId": "197", "severity": 1, "message": "245", "line": 36, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 36, "endColumn": 10}, {"ruleId": "197", "severity": 1, "message": "246", "line": 37, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 37, "endColumn": 22}, {"ruleId": "197", "severity": 1, "message": "247", "line": 38, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 38, "endColumn": 11}, {"ruleId": "197", "severity": 1, "message": "248", "line": 39, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 39, "endColumn": 6}, {"ruleId": "197", "severity": 1, "message": "217", "line": 46, "column": 9, "nodeType": "199", "messageId": "200", "endLine": 46, "endColumn": 14}, {"ruleId": "197", "severity": 1, "message": "249", "line": 71, "column": 17, "nodeType": "199", "messageId": "200", "endLine": 71, "endColumn": 28}, {"ruleId": "197", "severity": 1, "message": "250", "line": 71, "column": 39, "nodeType": "199", "messageId": "200", "endLine": 71, "endColumn": 53}, {"ruleId": "197", "severity": 1, "message": "251", "line": 76, "column": 17, "nodeType": "199", "messageId": "200", "endLine": 76, "endColumn": 31}, {"ruleId": "197", "severity": 1, "message": "252", "line": 76, "column": 42, "nodeType": "199", "messageId": "200", "endLine": 76, "endColumn": 55}, {"ruleId": "253", "severity": 1, "message": "254", "line": 95, "column": 6, "nodeType": "255", "endLine": 95, "endColumn": 8, "suggestions": "256"}, {"ruleId": "253", "severity": 1, "message": "257", "line": 63, "column": 19, "nodeType": "199", "endLine": 63, "endColumn": 26}, {"ruleId": "253", "severity": 1, "message": "258", "line": 114, "column": 6, "nodeType": "255", "endLine": 114, "endColumn": 30, "suggestions": "259"}, {"ruleId": "253", "severity": 1, "message": "260", "line": 114, "column": 14, "nodeType": "261", "endLine": 114, "endColumn": 29}, {"ruleId": "253", "severity": 1, "message": "260", "line": 64, "column": 27, "nodeType": "261", "endLine": 64, "endColumn": 42}, {"ruleId": "197", "severity": 1, "message": "262", "line": 2, "column": 10, "nodeType": "199", "messageId": "200", "endLine": 2, "endColumn": 26}, {"ruleId": "197", "severity": 1, "message": "263", "line": 23, "column": 15, "nodeType": "199", "messageId": "200", "endLine": 23, "endColumn": 27}, "@typescript-eslint/no-unused-vars", "'Card' is defined but never used.", "Identifier", "unusedVar", "'CardContent' is defined but never used.", "'TextField' is defined but never used.", "'Chip' is defined but never used.", "'List' is defined but never used.", "'ListItem' is defined but never used.", "'ListItemText' is defined but never used.", "'ListItemSecondaryAction' is defined but never used.", "'IconButton' is defined but never used.", "'Dialog' is defined but never used.", "'DialogTitle' is defined but never used.", "'DialogContent' is defined but never used.", "'DialogActions' is defined but never used.", "'Alert' is defined but never used.", "'DeleteIcon' is defined but never used.", "'ClearIcon' is defined but never used.", "'WarmIcon' is defined but never used.", "'theme' is assigned a value but never used.", "'setSearchPattern' is assigned a value but never used.", "'confirmDialog' is assigned a value but never used.", "'cacheConfig' is assigned a value but never used.", "'configLoading' is assigned a value but never used.", "'cacheKeys' is assigned a value but never used.", "'keysLoading' is assigned a value but never used.", "'handleSearchKeys' is assigned a value but never used.", "'handleInvalidateAll' is assigned a value but never used.", "'handleCleanupExpired' is assigned a value but never used.", "'handleWarmCache' is assigned a value but never used.", "'ErrorIcon' is defined but never used.", "'AreaChart' is defined but never used.", "'Area' is defined but never used.", "'performanceDashboard' is assigned a value but never used.", "'asyncSearchPerf' is assigned a value but never used.", "'Table' is defined but never used.", "'TableBody' is defined but never used.", "'TableCell' is defined but never used.", "'TableContainer' is defined but never used.", "'TableHead' is defined but never used.", "'TableRow' is defined but never used.", "'Paper' is defined but never used.", "'LineChart' is defined but never used.", "'Line' is defined but never used.", "'XAxis' is defined but never used.", "'YAxis' is defined but never used.", "'CartesianGrid' is defined but never used.", "'Tooltip' is defined but never used.", "'ResponsiveContainer' is defined but never used.", "'BarChart' is defined but never used.", "'Bar' is defined but never used.", "'warmingPerf' is assigned a value but never used.", "'warmingLoading' is assigned a value but never used.", "'servicesHealth' is assigned a value but never used.", "'healthLoading' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'handleRefresh'. Either include it or remove the dependency array.", "ArrayExpression", ["264"], "The ref value 'handlersRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'handlersRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "React Hook useEffect has a missing dependency: 'handler'. Either include it or remove the dependency array. If 'handler' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["265"], "React Hook useEffect has a spread element in its dependency array. This means we can't statically verify whether you've passed the correct dependencies.", "SpreadElement", "'WebSocketMessage' is defined but never used.", "'DownloadIcon' is defined but never used.", {"desc": "266", "fix": "267"}, {"desc": "268", "fix": "269"}, "Update the dependencies array to be: [handleRefresh]", {"range": "270", "text": "271"}, "Update the dependencies array to be: [event, handler]", {"range": "272", "text": "273"}, [2381, 2383], "[handleRefresh]", [3177, 3201], "[event, handler]"]