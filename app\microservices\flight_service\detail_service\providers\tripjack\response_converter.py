from app.microservices.flight_service.utils.utils import extract_terminal_number, format_fare_identifier, minutes_to_hours_minutes

def review_translate_provider_to_client(request,provider_response,context):
    # Enhanced logging for debugging TripJack response structure
    print("=" * 80)
    print("🔍 TRIPJACK DETAIL RESPONSE CONVERTER DEBUG")
    print("=" * 80)
    print(f"Request: {request}")
    print(f"Context: {context}")
    print(f"Provider Response Type: {type(provider_response)}")
    print(f"Provider Response: {provider_response}")

    # Check for None response
    if provider_response is None:
        print("❌ ERROR: Provider response is None!")
        print("=" * 80)
        raise ValueError("Provider response is None")

    # Check for required fields
    required_fields = ['tripInfos', 'searchQuery', 'totalPriceInfo']
    for field in required_fields:
        if field not in provider_response:
            print(f"❌ ERROR: Missing required field '{field}' in provider response")
        else:
            print(f"✅ Found field '{field}': {type(provider_response[field])}")

    print("=" * 80)
    def map_timed_fare_rule(fare_rule_info):
        print(f"🔍 Processing fare rule info: {fare_rule_info}")

        # Check for None or empty fare_rule_info
        if not fare_rule_info:
            print("❌ WARNING: fare_rule_info is None or empty, returning empty rules")
            return []

        mapped_response = []

        # Helper function to map a single policy item to the desired format
        def map_single_policy(policy):
            if not policy:
                print("❌ WARNING: policy is None or empty")
                return {
                    "AdultAmount": "",
                    "ChildAmount": "",
                    "InfantAmount": "",
                    "YouthAmount": None,
                    "Description": "",
                    "CurrencyCode": "",
                    "TimeDay": None
                }
            return {
                "AdultAmount": str(policy.get("amount", 0)) if policy.get("amount") else policy.get('policyInfo', ''),  # Amount for adults
                "ChildAmount": str(policy.get("amount", 0)) if policy.get("amount") else "",  # Amount for children
                "InfantAmount": "",  # Amount for infants (currently empty)
                "YouthAmount": None,  # Amount for youth (currently None)
                "Description": policy.get("pp", "").replace("_", " ").title(),  # Policy description formatted
                "CurrencyCode": "",  # Currency Field (currently empty)
                "TimeDay": None  # Time/day field (currently None)
            }

        # Process the keys within the 'tfr' field
        if "tfr" in fare_rule_info and fare_rule_info["tfr"]:
            tfr = fare_rule_info["tfr"]

            # Process CANCELLATION policies
            if "CANCELLATION" in tfr:
                cancellation_info = []
                for policy in tfr["CANCELLATION"]:
                    cancellation_info.append(map_single_policy(policy))
                mapped_response.append({
                    "Info": cancellation_info,
                    "Head": "Cancellation(Per Pax/ Per Journey)"
                })

            # Process NO_SHOW policies
            if "NO_SHOW" in tfr:
                no_show_info = []
                for policy in tfr["NO_SHOW"]:
                    no_show_info.append(map_single_policy(policy))
                mapped_response.append({
                    "Info": no_show_info,
                    "Head": "NO Show(Per Pax/ Per Journey)"
                })

            # Process DATECHANGE policies
            if "DATECHANGE" in tfr:
                date_change_info = []
                for policy in tfr["DATECHANGE"]:
                    date_change_info.append(map_single_policy(policy))
                mapped_response.append({
                    "Info": date_change_info,
                    "Head": "Date Change(Per Pax/ Per Journey)"
                })

            # Process SEAT_CHARGEABLE policies (if exists)
            if "SEAT_CHARGEABLE" in tfr:
                seat_chargeable_info = []
                for policy in tfr["SEAT_CHARGEABLE"]:
                    seat_chargeable_info.append(map_single_policy(policy))
                mapped_response.append({
                    "Info": seat_chargeable_info,
                    "Head": "Seat Chargeable(Per Pax/ Per Journey)"
                })

        return mapped_response

    trips = []
    """ Fare Component """
    total_fare = provider_response.get('totalPriceInfo').get('totalFareDetail').get('fC').get('TF')
    total_tax_and_fee = provider_response.get('totalPriceInfo').get('totalFareDetail').get('fC').get('TAF')
    total_base_fare = provider_response.get('totalPriceInfo').get('totalFareDetail').get('fC').get('BF')

    adult = provider_response['searchQuery']['paxInfo']['ADULT']
    child = provider_response['searchQuery']['paxInfo']['CHILD']
    infant = provider_response['searchQuery']['paxInfo']['INFANT']

    for trip in provider_response['tripInfos']:
        print(f"🔍 Processing trip: {trip}")

        Segments = []
        # TODO: If multiple totalPriceList the logic needs to be changed for general informations

        # Safe access to totalPriceList with detailed logging
        total_price_list = trip.get("totalPriceList", [])
        print(f"Total price list: {total_price_list}")

        if not total_price_list:
            print("❌ ERROR: totalPriceList is empty or missing")
            continue

        first_price = total_price_list[0] if total_price_list else {}
        print(f"First price: {first_price}")

        fd = first_price.get('fd', {}) if first_price else {}
        print(f"FD (fare details): {fd}")

        adult_fare = fd.get('ADULT', {}) if fd else {}
        print(f"Adult fare: {adult_fare}")

        # Safe extraction with fallbacks
        cabin_class = adult_fare.get('cc', '') if adult_fare else ''
        refundable = "N" if adult_fare.get('rT', 0) == 0 else "Y" if adult_fare else "N"
        seats = adult_fare.get('sR', 0) if adult_fare else 0
        fare_basis = adult_fare.get('fB', '') if adult_fare else ''
        fare_class = adult_fare.get('cB', '') if adult_fare else ''
        total_duration_minutes = 0

        print(f"Extracted values - cabin_class: {cabin_class}, refundable: {refundable}, seats: {seats}, fare_basis: {fare_basis}, fare_class: {fare_class}")

        # Safe access to sI (segment information)
        si_list = trip.get('sI', [])
        print(f"SI (segment info) list: {si_list}")

        if not si_list:
            print("❌ ERROR: sI (segment info) is empty or missing")
            continue

        # Safe extraction of journey start and end
        first_segment = si_list[0] if si_list else {}
        last_segment = si_list[-1] if si_list else {}

        journey_start = ''
        journey_end = ''

        if first_segment and 'da' in first_segment and first_segment['da']:
            journey_start = first_segment['da'].get('code', '')

        if last_segment and 'aa' in last_segment and last_segment['aa']:
            journey_end = last_segment['aa'].get('code', '')

        print(f"Journey: {journey_start} -> {journey_end}")

        # Safe access to fare rule information
        fare_rule_info = first_price.get('fareRuleInformation', {}) if first_price else {}
        print(f"Fare rule info: {fare_rule_info}")

        FareRules = [
                        {
                            "OrginDestination": f"{journey_start}-{journey_end}",
                            "FareRuleText": None,
                            "Rule": map_timed_fare_rule(fare_rule_info)
                        }
                    ]
        for idx, segment in enumerate(trip.get('sI',[])):
            PTCFare = []
            SSR = []
            if adult:
                PTCFare.append({    "SSRDiscount": 0,
                                    "SSRMarkup": 0,
                                    "AgentMarkUp": 0,
                                    "Ammendment": 0,
                                    "API": 0,
                                    "CGST": 0,
                                    "CUTE": 0,
                                    "Fare": trip.get('totalPriceList')[0].get('fd').get('ADULT').get('fC').get('BF'),
                                    "GrossFare": total_fare,
                                    "IGST": 0,
                                    "K3": 0,
                                    "K7": 0,
                                    "NetFare": total_fare,
                                    "OT": f"{trip.get('totalPriceList')[0].get('fd').get('ADULT').get('afC').get('TAF').get('OT')}", #f"{OTT_charge}",
                                    "OTT":"",
                                    "PHF": 0,
                                    "PSF": 0,
                                    "PTC": "ADT",
                                    "RCF": 0,
                                    "RCS": 0,
                                    "ReissueCharge": 0,
                                    "SGST": 0,
                                    "ST": 0,
                                    "Tax": trip.get('totalPriceList')[0].get('fd').get('ADULT').get('fC').get('TAX'),
                                    "TransactionFee": 0,
                                    "UD": trip.get('totalPriceList')[0].get('fd').get('ADULT').get('afC').get('TAF').get('AGST'),
                                    "YQ": trip.get('totalPriceList')[0].get('fd').get('ADULT').get('afC').get('TAF').get('YQ'),  #YQ_charge,
                                    "YR": 0
                                            })

                baggageIB = f"{trip.get('totalPriceList')[0].get('fd').get('ADULT').get('bI').get('iB')}"
                baggageCB = f"{trip.get('totalPriceList')[0].get('fd').get('ADULT').get('bI').get('cB')}"
                """
                bI-Baggageinformation(HandBaggage&CheckingbaggageRelated)
                    ● iB - Checking Baggage (No format specific, example : 20Kg , 20 Kg, 1 Piece , 1
                    Piece, 20 Kilograms)
                    ● cB - Cabin Baggage
                """
                SSR.append({
                        "Code": "BAG",
                        "Description": f"{baggageIB},{baggageCB}",
                        "PieceDescription": "",
                        "Charge": 0.0,
                        "OrginalCharge": 0.0,
                        "GST": 0.0,
                        "Type": "2",
                        "Category": "",
                        "PTC": "ADT",
                        "ID": 1,
                        "IsFreeMeal": True,
                        "MealImage": "",
                        "Url": None,
                        "OriginID": 0,
                        "OriginCharge": 0.0,
                        "AdditionalField": []
                            })
            if child:
                PTCFare.append({    "SSRDiscount": 0,
                                    "SSRMarkup": 0,
                                    "AgentMarkUp": 0,
                                    "Ammendment": 0,
                                    "API": 0,
                                    "CGST": 0,
                                    "CUTE": 0,
                                    "Fare": trip.get('totalPriceList')[0].get('fd').get('CHILD').get('fC').get('BF'),
                                    "GrossFare": total_fare,
                                    "IGST": 0,
                                    "K3": 0,
                                    "K7": 0,
                                    "NetFare": total_fare,
                                    "OldSSRAmount": 0,
                                    "OT": f"{trip.get('totalPriceList')[0].get('fd').get('CHILD').get('afC').get('TAF').get('OT')}", # f"{OTT_charge}",
                                    "OTT":"",
                                    "PHF": 0,
                                    "PSF": 0,
                                    "PTC": "CHD",
                                    "RCF": 0,
                                    "RCS": 0,
                                    "ReissueCharge": 0,
                                    "SGST": 0,
                                    "ST": 0,
                                    "Tax": trip.get('totalPriceList')[0].get('fd').get('CHILD').get('fC').get('TAX'),
                                    "TransactionFee": 0,
                                    "UD": trip.get('totalPriceList')[0].get('fd').get('CHILD').get('afC').get('TAF').get('AGST'),
                                    "YQ": trip.get('totalPriceList')[0].get('fd').get('CHILD').get('afC').get('TAF').get('YQ'), #YQ_charge,
                                    "YR": 0,
                                            })
                baggageIB = f"{trip.get('totalPriceList')[0].get('fd').get('CHILD').get('bI').get('iB')}"
                baggageCB = f"{trip.get('totalPriceList')[0].get('fd').get('CHILD').get('bI').get('cB')}"
                SSR.append({
                        "Code": "BAG",
                        "Description": f"{baggageIB},{baggageCB}",
                        "PieceDescription": "",
                        "Charge": 0.0,
                        "OrginalCharge": 0.0,
                        "GST": 0.0,
                        "Type": "2",
                        "Category": "",
                        "PTC": "CHD",
                        "ID": 1,
                        "IsFreeMeal": True,
                        "MealImage": "",
                        "Url": None,
                        "OriginID": 0,
                        "OriginCharge": 0.0,
                        "AdditionalField": []
                            })
            if infant:
                PTCFare.append({    "SSRDiscount": 0,
                                    "SSRMarkup": 0,
                                    "AgentMarkUp": 0,
                                    "Ammendment": 0,
                                    "API": 0,
                                    "CGST": 0,
                                    "CUTE": 0,
                                    "Fare": trip.get('totalPriceList')[0].get('fd').get('INFANT').get('fC').get('BF'),
                                    "GrossFare": total_fare,
                                    "IGST": 0,
                                    "K3": 0,
                                    "K7": 0,
                                    "NetFare": total_fare,
                                    "OldSSRAmount": 0,
                                    "OT": f"{trip.get('totalPriceList')[0].get('fd').get('INFANT').get('afC').get('TAF').get('OT')}",
                                    "OTT":"",
                                    "PHF": 0,
                                    "PSF": 0,
                                    "PTC": "INF",
                                    "RCF": 0,
                                    "RCS": 0,
                                    "ReissueCharge": 0,
                                    "SGST": 0,
                                    "ST": 0,
                                    "Tax": trip.get('totalPriceList')[0].get('fd').get('INFANT').get('fC').get('TAX'),
                                    "TransactionFee": 0,
                                    "UD": trip.get('totalPriceList')[0].get('fd').get('INFANT').get('afC').get('TAF').get('AGST'),
                                    "YQ": trip.get('totalPriceList')[0].get('fd').get('INFANT').get('afC').get('TAF').get('YQ'),
                                    "YR": 0
                                            })
                baggageIB = f"{trip.get('totalPriceList')[0].get('fd').get('INFANT').get('bI').get('iB')}"
                baggageCB = f"{trip.get('totalPriceList')[0].get('fd').get('INFANT').get('bI').get('cB')}"
                SSR.append({
                        "Code": "BAG",
                        "Description": f"{baggageIB},{baggageCB}",
                        "PieceDescription": "",
                        "Charge": 0.0,
                        "OrginalCharge": 0.0,
                        "GST": 0.0,
                        "Type": "2",
                        "Category": "",
                        "PTC": "INF",
                        "ID": 1,
                        "IsFreeMeal": True,
                        "MealImage": "",
                        "Url": None,
                        "OriginID": 0,
                        "OriginCharge": 0.0,
                        "AdditionalField": []
                            })

            """ For find duration cal start and end -> duration """
            total_duration_minutes += segment.get('duration') + segment.get('cT',0)
            airline = f'{segment.get("fD", {}).get("aI", {}).get("name", "")}|{segment.get("fD", {}).get("aI", {}).get("name", "")}|{segment.get("fD", {}).get("aI", {}).get("name", "")}'
            segmentData = {
                # Unique Flight Identifier for this segment, can be a list of IDs
                "FUID": "1,2",

                # Value-Added Carrier (if any, generally for marketing purposes)
                "VAC": "",

                # Information about the fares related to this segment
                "Fares": {
                    "GrossFare": total_fare,  # Total fare before any deductions
                    "NetFare": total_fare,    # Fare after deductions (currently same as GrossFare)
                    "OldSSRAmount": 0,        # Old SSR (Special Service Request) amount, typically for tracking
                    "PTCFare": PTCFare,       # Potential Ticket Charge Fare, specific to pricing
                    "TotalBaseFare": total_base_fare,  # Base fare excluding taxes and fees
                    "TotalTax": total_tax_and_fee,     # Total tax and any additional fees
                    "TotalAddonDiscount": 0,  # Any discounts applied to add-ons
                    "TotalAddonMarkup": 0,     # Additional markup on add-ons
                    "TotalAgentMarkUp": 0,     # Markup amount charged by the agent    # Charges for any additional services
                    "TotalCommission": 0,       # Total commission for agents or service providers
                    "TotalReissueCharge": 0,    # Charge for reissuing a ticket
                    "TotalServiceTax": 0,       # Tax charged on the service
                    "TotalTransactionFee": 0,    # Fees associated with the transaction
                },

                # Flight information related to this segment
                "Flight": {
                    "AirlineName": airline,   # Name of the airline operating the flight
                    "AirCraft": segment.get("fD", {}).get("eT", ""),  # Type of aircraft being used
                    "Airline": airline,       # Repeat of airline name for clarity
                    "Amenities": None,       # Any onboard amenities available (if applicable)

                    # Arrival airport information
                    "ArrAirportName": f'{segment.get("aa").get("name","")} |{segment.get("aa").get("city","")}',  # Name and city of arrival airport
                    "ArrivalCode": segment.get('aa').get('code',''),  # Code for the arrival airport
                    "ArrivalTerminal": extract_terminal_number(segment.get('aa').get('terminal',"")),  # Extract terminal number for arrival
                    "ArrivalTime": segment.get('at'),  # Scheduled arrival time of the flight

                    "Cabin": cabin_class,      # Cabin class (e.g., Economy, Business)
                    "CarbonEmissions": 0,     # Carbon emissions associated with the flight (currently zero)

                    # Departure airport information
                    "DepAirportName": f'{segment.get("da").get("name","")} |{segment.get("da").get("city","")}',  # Name and city of departure airport
                    "DepartureCode": segment.get('da').get('code',''),  # Code for the departure airport
                    "DepartureTerminal": extract_terminal_number(segment.get('da').get('terminal',"")),  # Extract terminal number for departure
                    "DepartureTime": segment.get('dt'),  # Scheduled departure time of the flight

                    "Duration": minutes_to_hours_minutes(segment.get('duration')),  # Flight duration converted to hours and minutes
                    "EquipmentType": "",       # Type of equipment used for the flight (to be verified)
                    "FareClass": fare_class,   # Class of fare (like economy, business, etc.)
                    "Farelink": None,          # Link to fare details, if applicable

                    # Fare basis information
                    "FareBasisCode": fare_basis,  # Fare basis code that determines pricing rules
                    "FCBegin": "",               # Start date for fare conditions (if applicable)
                    "FCEnd": "",                 # End date for fare conditions (if applicable)

                    # Flight details
                    "Provider": segment.get("fD", {}).get("aI", {}).get("code", ""),  # Provider code (e.g., airline code)
                    "FlightNo": segment.get("fD", {}).get("fN", ""),  # Flight number
                    "FUID": idx + 1,           # Unique identifier for the flight segment
                    "Hops": None,              # Additional hops or legs in the journey (if applicable)

                    # Airline codes
                    "VAC": segment.get("fD", {}).get("aI", {}).get("code", ""),  # Value-Added Carrier code
                    "MAC": segment.get("fD", {}).get("aI", {}).get("code", ""),  # Marketing Airline Code
                    "OAC": segment.get("fD", {}).get("aI", {}).get("code", ""),  # Operating Airline Code (needs verification)

                    "RBD": "",                  # Reservation Booking Designator (if applicable)
                    "Refundable": refundable,   # Whether the ticket is refundable or not
                    "Seats": seats,             # Number of available seats on this flight
                },

                # Special Service Requests associated with this flight segment
                "SSR": SSR,
            }
            if idx == 0:
                segmentData["Rules"] = FareRules
                """ Fare Rule will Journey """
            Segments.append(segmentData)

        journey = {
            "From": journey_start,  # Starting point of the journey (departure location)
            "To": journey_end,  # Ending point of the journey (destination)
            "Journey": [{
                "ChannelCode": "",  # Channel through which the booking was made (e.g., website, mobile app, etc.)
                "Duration": minutes_to_hours_minutes(total_duration_minutes),  # Total duration of the journey formatted as hours and minutes
                "FCType": "",  # Fare Class type (e.g., standard, promotional fare, etc.)
                "GrossFare": total_fare,  # Total fare before any deductions
                "NetFare": total_fare,  # Total fare after deductions (currently same as GrossFare)
                "Notices": [],  # List of notices or alerts relevant to this journey (e.g., flight changes, reminders)
                "OrderID": 0,  # Unique Order ID for tracking the journey (set to 0 if not applicable)
                "Promo": None,  # Promotional details if available, otherwise None
                "Provider": "TJ",  # Provider code indicating the service provider (here it is "TJ")
                "SeatHold": False,  # Indicates whether seats are held (usually False if not held)
                "Segments": Segments,  # Segments of the journey, representing individual legs of the trip
                "Stops": len(trip.get('sI', [])) - 1  # Number of stops in the journey (calculated from the segments)
            }]
        }
        trips.append(journey)  # Append the journey to the trips list

    client_response = {
        "Code": "200",  # HTTP status code indicating success
        "Msg": ["Success"],  # Message associated with the operation status
        "From": provider_response['searchQuery']['routeInfos'][0]['fromCityOrAirport']['code'],  # Departure city or airport code
        "To": provider_response['searchQuery']['routeInfos'][0]['toCityOrAirport']['code'],  # Arrival city or airport code
        "FromName": provider_response['searchQuery']['routeInfos'][0]['fromCityOrAirport']['name'],  # Name of the departure city or airport
        "ToName": provider_response['searchQuery']['routeInfos'][0]['toCityOrAirport']['name'],  # Name of the arrival city or airport
        "OnwardDate": provider_response['searchQuery']['routeInfos'][0]['travelDate'],  # Date of travel for the onward journey
        "ADT": adult,  # Number of adult passengers
        "CHD": child,  # Number of child passengers
        "INF": infant,  # Number of infant passengers
        "NetAmount": provider_response['totalPriceInfo']['totalFareDetail']['fC']['NF'],  # Net fare amount after discounts or deductions
        "GrossAmount": provider_response['totalPriceInfo']['totalFareDetail']['fC']['TF'],  # Gross fare amount before any deductions
        "FareType":  format_fare_identifier(fareIdentifier=provider_response['tripInfos'][0]['totalPriceList'][0]['fareIdentifier'], fare_class=cabin_class),  # Type of fare (e.g., standard, promotional)
        "Trips": trips  # List of trips included in the response
    }
    client_response['TUI'] = context.get('TUI')  # Adding TUI information from the context, if available
    return client_response  # Returning the final client response